# 农场配置系统快速开始指南

## 🚀 快速开始

### 开发环境初始化

```bash
# 1. 安装依赖
npm install

# 2. 运行数据库迁移
npm run migrate
# 或者手动运行
node migrations/20250721000000-create-farm-configs.js

# 3. 初始化配置数据
node scripts/init-farm-config.js

# 4. 验证初始化
node scripts/init-farm-config.js --status

# 5. 启动应用
npm start
```

### 生产环境部署

```bash
# 1. 完整部署（包含备份、迁移、初始化、验证）
node scripts/deploy-farm-config.js

# 2. 如果部署失败，可以回滚
node scripts/deploy-farm-config.js --rollback
```

## 📊 初始化数据说明

系统将自动导入以下配置：

- **等级范围**: 0-50级（共51条配置）
- **数据来源**: fram.xlsx文件的真实数据
- **版本号**: v1.0.0-initial
- **激活状态**: 自动激活

### 配置数据结构

每个等级包含以下字段：
- `grade`: 等级 (0-50)
- `production`: 每秒产出计算用值
- `cow`: 奶牛数量
- `speed`: 生产速度百分比
- `milk`: 牛奶生产量
- `cost`: 升级花费
- `offline`: 离线产出

## 🔧 管理接口使用

### 1. 获取当前配置
```bash
curl http://localhost:3000/api/admin/farm-config/current
```

### 2. 上传新配置
```bash
curl -X POST \
  -F "excelFile=@fram.xlsx" \
  -F "versionName=新版本名称" \
  -F "description=版本描述" \
  http://localhost:3000/api/admin/farm-config/upload
```

### 3. 激活版本
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-Confirm-Operation: true" \
  -d '{"version":"20250721-163045","remark":"激活原因"}' \
  http://localhost:3000/api/admin/farm-config/activate
```

### 4. 获取版本列表
```bash
curl http://localhost:3000/api/admin/farm-config/versions
```

## 🧪 测试验证

### 运行完整测试套件
```bash
node scripts/run-all-farm-config-tests.js
```

### 单独运行各种测试
```bash
# 迁移验证
node scripts/validate-migration.js

# 集成测试
node scripts/test-farm-config-integration.js

# 性能测试
node scripts/performance-test-farm-config.js
```

## 📋 常用命令

### 配置管理
```bash
# 查看当前状态
node scripts/init-farm-config.js --status

# 强制重新初始化
node scripts/init-farm-config.js --force

# 预热缓存
curl -X POST http://localhost:3000/api/admin/farm-config/warmup-cache
```

### 数据库操作
```bash
# 备份数据库
mysqldump -u username -p database_name > backup.sql

# 恢复数据库
mysql -u username -p database_name < backup.sql
```

## 🔍 故障排除

### 常见问题

1. **初始化失败**
   ```bash
   # 检查数据库连接
   mysql -u username -p -e "SELECT 1"
   
   # 检查表是否存在
   mysql -u username -p database_name -e "SHOW TABLES LIKE 'farm_%'"
   ```

2. **配置查询失败**
   ```bash
   # 检查激活配置
   mysql -u username -p database_name -e "SELECT COUNT(*) FROM farm_configs WHERE isActive = 1"
   
   # 检查Redis缓存
   redis-cli GET farm_config:active
   ```

3. **权限问题**
   ```bash
   # 检查管理员配置
   echo $ADMIN_WALLETS
   
   # 开发环境测试
   NODE_ENV=development node scripts/test-admin-access.js
   ```

## 📈 监控指标

### 关键指标
- 配置查询响应时间: < 50ms
- 缓存命中率: > 90%
- 数据库连接数: < 100
- 错误率: < 1%

### 监控命令
```bash
# 检查应用状态
curl http://localhost:3000/api/health

# 检查配置统计
curl http://localhost:3000/api/admin/farm-config/stats

# 检查Redis状态
redis-cli INFO stats
```

## 🔄 版本管理工作流

### 1. 策划更新配置
1. 修改fram.xlsx文件
2. 通过管理后台上传新配置
3. 验证配置数据
4. 激活新版本

### 2. 开发环境测试
```bash
# 上传测试配置
curl -X POST -F "excelFile=@test_config.xlsx" \
  http://localhost:3000/api/excel/farm-config/upload

# 验证配置
curl -X POST -F "excelFile=@test_config.xlsx" \
  http://localhost:3000/api/excel/farm-config/validate

# 激活测试版本
curl -X POST -H "Content-Type: application/json" \
  -d '{"version":"test_version"}' \
  http://localhost:3000/api/admin/farm-config/activate
```

### 3. 生产环境发布
```bash
# 备份当前配置
node scripts/backup-current-config.js

# 上传新配置
# (通过管理后台操作)

# 激活新配置
# (通过管理后台操作)

# 验证发布结果
node scripts/validate-migration.js
```

## 📞 支持

如果遇到问题：

1. 查看日志文件
2. 运行诊断脚本
3. 检查监控指标
4. 联系技术支持

---

**注意**: 在生产环境操作前，请务必备份数据并在测试环境验证所有功能。
