// 农场配置数据迁移脚本
// 将现有的硬编码配置数据迁移到数据库中

const { sequelize } = require('../src/config/db');
const { FarmConfig } = require('../src/models/FarmConfig');
const { FarmConfigVersion } = require('../src/models/FarmConfigVersion');

// 从现有配置文件导入数据
const {
  FARM_PLOT_BARN_COUNT,
  FARM_PLOT_PRODUCTION_SPEED,
  FARM_PLOT_MILK_PRODUCTION,
  FARM_PLOT_UPGRADE_COST
} = require('../src/config/farmPlotConfig');

/**
 * 生成基于现有配置的数据
 * 注意：这里需要根据实际的配置数据结构进行调整
 */
function generateConfigData() {
  const configs = [];
  
  // 根据现有配置生成0-50级的配置数据
  // 这里使用示例数据，实际应该从fram.xlsx文件中读取
  for (let grade = 0; grade <= 50; grade++) {
    let production = 0;
    let cow = 0;
    let speed = 100;
    let milk = 0;
    let cost = 0;
    let offline = 0;

    if (grade === 0) {
      // 0级特殊处理
      production = 0;
      cow = 0;
      speed = 0;
      milk = 0;
      cost = 13096;
      offline = 0;
    } else if (grade <= 20) {
      // 1-20级使用现有配置数据
      const levelIndex = grade - 1;
      
      if (levelIndex < FARM_PLOT_BARN_COUNT.length) {
        cow = FARM_PLOT_BARN_COUNT[levelIndex];
      } else {
        cow = grade; // 线性增长
      }
      
      if (levelIndex < FARM_PLOT_PRODUCTION_SPEED.length) {
        speed = Math.round((5 / FARM_PLOT_PRODUCTION_SPEED[levelIndex]) * 100);
      } else {
        speed = 100 + grade * 5; // 基础增长
      }
      
      // 使用第一个农场区块的数据作为基准
      if (FARM_PLOT_MILK_PRODUCTION[0] && FARM_PLOT_MILK_PRODUCTION[0][levelIndex]) {
        milk = FARM_PLOT_MILK_PRODUCTION[0][levelIndex];
      } else {
        milk = grade * 1.5; // 基础增长
      }
      
      if (FARM_PLOT_UPGRADE_COST[0] && FARM_PLOT_UPGRADE_COST[0][levelIndex]) {
        cost = FARM_PLOT_UPGRADE_COST[0][levelIndex];
      } else {
        cost = Math.round(100 * Math.pow(1.5, grade)); // 指数增长
      }
      
      production = Math.round(milk * speed / 100);
      offline = milk * 0.5; // 离线产出为正常产出的50%
    } else {
      // 21-50级使用扩展数据（基于文档中的数据）
      // 这里应该从fram.xlsx文件中读取实际数据
      cow = Math.min(20, grade); // 最多20只奶牛
      speed = 100 + Math.floor(grade / 2) * 10; // 每2级增加10%
      milk = grade * 2.5; // 基础增长
      cost = Math.round(1000 * Math.pow(2, grade - 20)); // 指数增长
      production = Math.round(milk * speed / 100);
      offline = milk * 0.5;
    }

    configs.push({
      grade,
      production,
      cow,
      speed,
      milk: parseFloat(milk.toFixed(3)),
      cost,
      offline: parseFloat(offline.toFixed(3))
    });
  }

  return configs;
}

/**
 * 执行数据迁移
 */
async function migrateData() {
  try {
    console.log('开始农场配置数据迁移...');

    // 确保数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 同步模型
    await sequelize.sync();
    console.log('数据库模型同步完成');

    // 检查是否已有默认配置
    const existingConfigs = await FarmConfig.findAll({
      where: { version: 'default' }
    });

    if (existingConfigs.length > 0) {
      console.log('默认配置已存在，跳过迁移');
      return;
    }

    // 生成配置数据
    const configData = generateConfigData();
    console.log(`生成了 ${configData.length} 条配置数据`);

    // 创建版本记录
    const version = 'default';
    const versionName = '默认配置';
    const description = '从现有硬编码配置迁移的默认配置数据';

    await FarmConfigVersion.createVersion(
      version,
      versionName,
      configData.length,
      description,
      'system'
    );
    console.log('版本记录创建成功');

    // 批量创建配置数据
    await FarmConfig.bulkCreateConfigs(configData, version, 'system');
    console.log('配置数据创建成功');

    // 激活默认版本
    await FarmConfig.activateVersion(version);
    await FarmConfigVersion.activateVersion(version);
    console.log('默认配置已激活');

    console.log('农场配置数据迁移完成！');

  } catch (error) {
    console.error('数据迁移失败:', error);
    throw error;
  }
}

/**
 * 验证迁移结果
 */
async function validateMigration() {
  try {
    console.log('验证迁移结果...');

    // 检查配置数量
    const configCount = await FarmConfig.count();
    console.log(`配置总数: ${configCount}`);

    // 检查激活配置
    const activeConfigs = await FarmConfig.getActiveConfigs();
    console.log(`激活配置数: ${activeConfigs.length}`);

    // 检查版本信息
    const versions = await FarmConfigVersion.getAllVersions();
    console.log(`版本总数: ${versions.length}`);

    const activeVersion = await FarmConfigVersion.getActiveVersion();
    console.log(`当前激活版本: ${activeVersion?.version || '无'}`);

    // 验证数据完整性
    const gradeRange = await FarmConfig.findAll({
      attributes: ['grade'],
      where: { isActive: true },
      order: [['grade', 'ASC']]
    });

    const grades = gradeRange.map(c => c.grade);
    const expectedGrades = Array.from({ length: 51 }, (_, i) => i);
    
    if (JSON.stringify(grades) === JSON.stringify(expectedGrades)) {
      console.log('✅ 数据完整性验证通过');
    } else {
      console.log('❌ 数据完整性验证失败');
      console.log('缺失等级:', expectedGrades.filter(g => !grades.includes(g)));
    }

    console.log('迁移验证完成');

  } catch (error) {
    console.error('验证失败:', error);
    throw error;
  }
}

// 主函数
async function main() {
  try {
    await migrateData();
    await validateMigration();
    console.log('🎉 农场配置系统迁移成功完成！');
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  migrateData,
  validateMigration,
  generateConfigData
};
