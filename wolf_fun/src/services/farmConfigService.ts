import { FarmConfig } from '../models/FarmConfig';
import { FarmConfigVersion } from '../models/FarmConfigVersion';
import { redis } from '../config/redis';
import * as XLSX from 'xlsx';
import dayjs from 'dayjs';

/**
 * 农场配置管理服务
 * 提供配置的CRUD操作、版本管理、缓存管理等功能
 */
export class FarmConfigService {
  // 缓存键前缀
  private static readonly CACHE_PREFIX = 'farm_config';
  private static readonly CACHE_TTL = 3600; // 1小时

  /**
   * 获取当前激活的配置
   */
  public static async getCurrentConfig(): Promise<FarmConfig[]> {
    const cacheKey = `${this.CACHE_PREFIX}:active`;
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Redis缓存读取失败:', error);
    }

    // 从数据库获取
    const configs = await FarmConfig.getActiveConfigs();
    
    // 更新缓存
    try {
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(configs));
    } catch (error) {
      console.warn('Redis缓存写入失败:', error);
    }

    return configs;
  }

  /**
   * 根据等级获取配置
   */
  public static async getConfigByGrade(grade: number): Promise<FarmConfig | null> {
    const cacheKey = `${this.CACHE_PREFIX}:active:grade:${grade}`;
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Redis缓存读取失败:', error);
    }

    // 从数据库获取
    const config = await FarmConfig.getActiveConfigByGrade(grade);
    
    // 更新缓存
    if (config) {
      try {
        await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(config));
      } catch (error) {
        console.warn('Redis缓存写入失败:', error);
      }
    }

    return config;
  }

  /**
   * 上传新配置（从Excel文件）
   */
  public static async uploadNewConfig(
    excelBuffer: Buffer,
    versionName: string,
    description?: string,
    createdBy?: string
  ): Promise<string> {
    // 解析Excel文件
    const configData = this.parseExcelFile(excelBuffer);
    
    // 验证数据
    const validation = FarmConfig.validateConfigData(configData);
    if (!validation.isValid) {
      throw new Error(`配置数据验证失败: ${validation.errors.join(', ')}`);
    }

    // 生成版本号
    const version = this.generateVersionNumber();

    // 创建版本记录
    await FarmConfigVersion.createVersion(
      version,
      versionName,
      configData.length,
      description,
      createdBy
    );

    // 批量创建配置数据
    await FarmConfig.bulkCreateConfigs(configData, version, createdBy);

    return version;
  }

  /**
   * 激活指定版本
   */
  public static async activateVersion(version: string): Promise<boolean> {
    // 检查版本是否存在
    const versionExists = await FarmConfigVersion.versionExists(version);
    if (!versionExists) {
      throw new Error(`版本 ${version} 不存在`);
    }

    // 激活配置
    const configActivated = await FarmConfig.activateVersion(version);
    const versionActivated = await FarmConfigVersion.activateVersion(version);

    if (configActivated && versionActivated) {
      // 清除缓存
      await this.clearCache();
      return true;
    }

    return false;
  }

  /**
   * 获取所有版本列表
   */
  public static async getAllVersions(): Promise<FarmConfigVersion[]> {
    const cacheKey = `${this.CACHE_PREFIX}:versions`;
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Redis缓存读取失败:', error);
    }

    // 从数据库获取
    const versions = await FarmConfigVersion.getAllVersions();
    
    // 更新缓存
    try {
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(versions));
    } catch (error) {
      console.warn('Redis缓存写入失败:', error);
    }

    return versions;
  }

  /**
   * 回滚到上一版本
   */
  public static async rollbackToPrevious(): Promise<boolean> {
    const versions = await this.getAllVersions();
    
    if (versions.length < 2) {
      throw new Error('没有可回滚的版本');
    }

    // 找到当前激活版本的索引
    const activeIndex = versions.findIndex(v => v.isActive);
    if (activeIndex === -1) {
      throw new Error('没有找到当前激活版本');
    }

    // 获取上一版本
    const previousVersion = versions[activeIndex + 1];
    if (!previousVersion) {
      throw new Error('没有可回滚的版本');
    }

    return await this.activateVersion(previousVersion.version);
  }

  /**
   * 删除指定版本
   */
  public static async deleteVersion(version: string): Promise<boolean> {
    // 删除配置数据
    const configDeleted = await FarmConfig.deleteVersion(version);
    
    // 删除版本记录
    const versionDeleted = await FarmConfigVersion.deleteVersion(version);

    if (configDeleted && versionDeleted) {
      // 清除相关缓存
      await this.clearVersionCache(version);
      return true;
    }

    return false;
  }

  /**
   * 获取版本统计信息
   */
  public static async getVersionStats(): Promise<{
    totalVersions: number;
    activeVersion: string | null;
    latestVersion: string | null;
    totalConfigs: number;
  }> {
    const versionStats = await FarmConfigVersion.getVersionStats();
    const totalConfigs = await FarmConfig.count();

    return {
      ...versionStats,
      totalConfigs
    };
  }

  /**
   * 解析Excel文件
   */
  private static parseExcelFile(buffer: Buffer): any[] {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      // 转换为JSON格式
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: ''
      });

      // 假设第一行是标题行
      const headers = jsonData[0] as string[];
      const dataRows = jsonData.slice(1);

      // 解析数据
      const configs = (dataRows as any[][]).map((row: any[]) => {
        const config: any = {};
        
        headers.forEach((header, index) => {
          const value = row[index];
          
          switch (header.toLowerCase()) {
            case 'grade':
            case '等级':
              config.grade = parseInt(value) || 0;
              break;
            case 'production':
            case '每秒产出计算用值':
              config.production = parseInt(value) || 0;
              break;
            case 'cow':
            case '奶牛数量':
              config.cow = parseInt(value) || 0;
              break;
            case 'speed':
            case '生产速度百分比':
              config.speed = parseInt(value) || 0;
              break;
            case 'milk':
            case '牛奶生产':
              config.milk = parseFloat(value) || 0;
              break;
            case 'cost':
            case '升级花费':
              config.cost = parseInt(value) || 0;
              break;
            case 'offline':
            case '离线产出':
              config.offline = parseFloat(value) || 0;
              break;
          }
        });

        return config;
      }).filter(config => config.grade !== undefined);

      return configs;
    } catch (error: any) {
      throw new Error(`Excel文件解析失败: ${error.message}`);
    }
  }

  /**
   * 生成版本号
   */
  private static generateVersionNumber(): string {
    return dayjs().format('YYYYMMDD-HHmmss');
  }

  /**
   * 清除所有相关缓存
   */
  private static async clearCache(): Promise<void> {
    try {
      const keys = [
        `${this.CACHE_PREFIX}:active`,
        `${this.CACHE_PREFIX}:active:grade:*`,
        `${this.CACHE_PREFIX}:versions`
      ];

      for (const key of keys) {
        if (key.includes('*')) {
          // 处理通配符键
          const matchingKeys = await redis.keys(key);
          if (matchingKeys.length > 0) {
            await redis.del(...matchingKeys);
          }
        } else {
          await redis.del(key);
        }
      }
    } catch (error) {
      console.warn('清除缓存失败:', error);
    }
  }

  /**
   * 清除指定版本的缓存
   */
  private static async clearVersionCache(version: string): Promise<void> {
    try {
      const keys = [
        `${this.CACHE_PREFIX}:version:${version}`,
        `${this.CACHE_PREFIX}:versions`
      ];

      await redis.del(...keys);
    } catch (error) {
      console.warn('清除版本缓存失败:', error);
    }
  }

  /**
   * 预热缓存
   */
  public static async warmupCache(): Promise<void> {
    try {
      console.log('开始预热农场配置缓存...');
      
      // 预热当前配置
      await this.getCurrentConfig();
      
      // 预热版本列表
      await this.getAllVersions();
      
      console.log('农场配置缓存预热完成');
    } catch (error) {
      console.warn('缓存预热失败:', error);
    }
  }
}
